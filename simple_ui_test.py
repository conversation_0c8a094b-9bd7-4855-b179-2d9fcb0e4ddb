#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UI测试
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """简单测试"""
    print("🎨 Testing Modern Login UI...")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("ScreenShot Pro")
        
        # 导入组件
        from src.ui.login_window import LoginWindow
        from src.core.auth_manager import AuthManager
        from src.themes.theme_manager import ThemeManager
        
        # 创建管理器
        auth_manager = AuthManager()
        theme_manager = ThemeManager()
        
        # 创建登录窗口
        login_window = LoginWindow(auth_manager, theme_manager)
        login_window.show()
        
        print("✅ Modern login window created successfully!")
        print("🎨 Features:")
        print("   • Glassmorphism card design")
        print("   • Modern gradient backgrounds")
        print("   • Smooth animations")
        print("   • Interactive feedback")
        print("   • Theme switching")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
