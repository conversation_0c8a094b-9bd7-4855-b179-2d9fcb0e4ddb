#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化UI演示程序
展示所有新的设计特性和动画效果
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt, QTimer

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.ui.login_window import LoginWindow
from src.core.auth_manager import AuthManager
from src.themes.theme_manager import ThemeManager

class ModernUIDemo:
    """现代化UI演示类"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_app()
        self.create_managers()
        self.create_login_window()
        self.show_welcome_message()
    
    def setup_app(self):
        """设置应用程序"""
        self.app.setApplicationName("ScreenShot Pro - Modern UI Demo")
        self.app.setApplicationVersion("1.0.0")
        
        # 支持高DPI显示
        try:
            self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
            self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
        except AttributeError:
            pass
    
    def create_managers(self):
        """创建管理器"""
        self.auth_manager = AuthManager()
        self.theme_manager = ThemeManager()
    
    def create_login_window(self):
        """创建登录窗口"""
        self.login_window = LoginWindow(self.auth_manager, self.theme_manager)
    
    def show_welcome_message(self):
        """显示欢迎消息"""
        welcome_msg = QMessageBox()
        welcome_msg.setWindowTitle("🎨 Modern UI Demo")
        welcome_msg.setText("""
        <h2>🚀 Welcome to ScreenShot Pro!</h2>
        <p>This demo showcases the new modern UI design with:</p>
        <ul>
        <li>✨ <b>Glassmorphism effects</b> - Beautiful translucent cards</li>
        <li>🎨 <b>Modern gradients</b> - Stunning background colors</li>
        <li>🔄 <b>Smooth animations</b> - Entrance, loading, and error animations</li>
        <li>🎯 <b>Interactive feedback</b> - Hover effects and focus states</li>
        <li>🌈 <b>Theme switching</b> - Starry and Hello Kitty themes</li>
        <li>📱 <b>Responsive design</b> - Optimized for all screen sizes</li>
        </ul>
        <p><b>Try these features:</b></p>
        <p>• Switch themes with the button at the bottom</p>
        <p>• Try logging in to see animations</p>
        <p>• Leave fields empty to see error animation</p>
        """)
        welcome_msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        welcome_msg.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 13px;
            }
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        """)
        
        # 显示消息后显示登录窗口
        welcome_msg.finished.connect(self.show_login_window)
        welcome_msg.exec()
    
    def show_login_window(self):
        """显示登录窗口"""
        self.login_window.show()
        
        # 5秒后自动演示主题切换
        QTimer.singleShot(5000, self.auto_demo_theme_switch)
    
    def auto_demo_theme_switch(self):
        """自动演示主题切换"""
        if self.login_window.isVisible():
            # 显示提示
            self.login_window.show_message("🎨 Auto-switching theme in 2 seconds...", "info")
            QTimer.singleShot(2000, self.login_window.toggle_theme)
    
    def run(self):
        """运行演示"""
        return self.app.exec()

def main():
    """主函数"""
    print("🎨 Starting Modern UI Demo...")
    print("=" * 50)
    print("Features to demonstrate:")
    print("✨ Glassmorphism effects")
    print("🎨 Modern gradient backgrounds")
    print("🔄 Smooth entrance animations")
    print("🎯 Interactive hover effects")
    print("🌈 Theme switching animations")
    print("📱 Responsive design elements")
    print("🚀 Loading and error animations")
    print("=" * 50)
    
    try:
        demo = ModernUIDemo()
        return demo.run()
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
