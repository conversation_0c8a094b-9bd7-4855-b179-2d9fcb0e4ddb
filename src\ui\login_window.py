#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化登录窗口 - 采用毛玻璃效果和现代设计语言
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QFrame, QMessageBox, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect, QParallelAnimationGroup
from PyQt6.QtGui import QFont, QPalette, QPixmap, QColor


class LoginWindow(QWidget):
    """现代化登录窗口类"""

    def __init__(self, auth_manager, theme_manager):
        super().__init__()
        self.auth_manager = auth_manager
        self.theme_manager = theme_manager

        self.init_ui()
        self.apply_theme()

        # 连接信号
        self.theme_manager.theme_changed.connect(self.apply_theme)

    def init_ui(self):
        """初始化现代化UI"""
        self.setWindowTitle("ScreenShot Pro")
        self.setFixedSize(480, 640)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)

        # 设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 主容器布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建毛玻璃卡片容器
        self.card_frame = QFrame()
        self.card_frame.setObjectName("loginCard")
        card_layout = QVBoxLayout(self.card_frame)
        card_layout.setSpacing(32)
        card_layout.setContentsMargins(48, 48, 48, 48)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.card_frame.setGraphicsEffect(shadow)

        # Logo和标题区域
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.setSpacing(16)

        # 现代化Logo图标
        logo_label = QLabel("🎯")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setStyleSheet("""
            font-size: 72px;
            margin-bottom: 8px;
        """)

        # 主标题
        title_label = QLabel("ScreenShot Pro")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("titleLabel")
        title_font = QFont("Segoe UI", 28, QFont.Weight.Bold)
        title_label.setFont(title_font)

        # 副标题
        subtitle_label = QLabel("Professional Screenshot & Editing Tool")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_font = QFont("Segoe UI", 13)
        subtitle_label.setFont(subtitle_font)

        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)

        # 表单区域
        form_frame = QFrame()
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(24)

        # 输入字段容器
        inputs_frame = QFrame()
        inputs_layout = QVBoxLayout(inputs_frame)
        inputs_layout.setSpacing(20)

        # 手机号输入组
        phone_container = QFrame()
        phone_container_layout = QVBoxLayout(phone_container)
        phone_container_layout.setSpacing(8)

        phone_label = QLabel("📱 Phone Number")
        phone_label.setObjectName("fieldLabel")
        phone_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("Enter your phone number")
        self.phone_input.setMaxLength(11)
        self.phone_input.setFixedHeight(56)
        self.phone_input.setObjectName("modernInput")

        phone_container_layout.addWidget(phone_label)
        phone_container_layout.addWidget(self.phone_input)

        # 密码输入组
        password_container = QFrame()
        password_container_layout = QVBoxLayout(password_container)
        password_container_layout.setSpacing(8)

        password_label = QLabel("🔒 Password")
        password_label.setObjectName("fieldLabel")
        password_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(56)
        self.password_input.setObjectName("modernInput")

        password_container_layout.addWidget(password_label)
        password_container_layout.addWidget(self.password_input)

        inputs_layout.addWidget(phone_container)
        inputs_layout.addWidget(password_container)

        # 登录按钮
        self.login_btn = QPushButton("Sign In")
        self.login_btn.setFixedHeight(56)
        self.login_btn.setObjectName("primaryButton")
        self.login_btn.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))

        # 注册链接区域
        register_frame = QFrame()
        register_layout = QHBoxLayout(register_frame)
        register_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        register_layout.setSpacing(8)

        register_text = QLabel("Don't have an account?")
        register_text.setObjectName("linkText")
        register_text.setFont(QFont("Segoe UI", 11))

        self.register_btn = QPushButton("Sign Up")
        self.register_btn.setFlat(True)
        self.register_btn.setObjectName("linkButton")
        self.register_btn.setFont(QFont("Segoe UI", 11, QFont.Weight.Bold))

        register_layout.addWidget(register_text)
        register_layout.addWidget(self.register_btn)

        # 添加控件到表单布局
        form_layout.addWidget(inputs_frame)
        form_layout.addWidget(self.login_btn)
        form_layout.addWidget(register_frame)

        # 底部区域
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setSpacing(16)
        footer_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 主题切换按钮
        self.theme_btn = QPushButton("🌙 Switch Theme")
        self.theme_btn.setFlat(True)
        self.theme_btn.setObjectName("themeButton")
        self.theme_btn.setFont(QFont("Segoe UI", 10))
        self.theme_btn.setFixedHeight(36)
        self.theme_btn.clicked.connect(self.toggle_theme)

        # 版本信息
        version_label = QLabel("v1.0.0 • Made with ❤️")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setObjectName("versionLabel")
        version_label.setFont(QFont("Segoe UI", 9))

        footer_layout.addWidget(self.theme_btn)
        footer_layout.addWidget(version_label)

        # 消息显示区域
        self.message_label = QLabel("")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setWordWrap(True)
        self.message_label.setObjectName("messageLabel")
        self.message_label.hide()

        # 添加到卡片布局
        card_layout.addWidget(header_frame)
        card_layout.addWidget(form_frame)
        card_layout.addWidget(footer_frame)
        card_layout.addWidget(self.message_label)

        # 居中卡片
        main_layout.addStretch()
        main_layout.addWidget(self.card_frame, 0, Qt.AlignmentFlag.AlignCenter)
        main_layout.addStretch()

        self.setLayout(main_layout)
        
        # 连接信号
        self.login_btn.clicked.connect(self.handle_login)
        self.register_btn.clicked.connect(self.show_register)
        self.password_input.returnPressed.connect(self.handle_login)

        # 添加输入框焦点事件
        self.phone_input.focusInEvent = self._create_focus_handler(self.phone_input, True)
        self.phone_input.focusOutEvent = self._create_focus_handler(self.phone_input, False)
        self.password_input.focusInEvent = self._create_focus_handler(self.password_input, True)
        self.password_input.focusOutEvent = self._create_focus_handler(self.password_input, False)

        # 初始化动画
        self._setup_animations()
        
    def apply_theme(self):
        """应用现代化主题"""
        current_theme = self.theme_manager.get_current_theme()

        if current_theme == 'starry':
            # 现代化星空主题
            self.setStyleSheet("""
                LoginWindow {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0f0c29, stop:0.5 #302b63, stop:1 #24243e);
                }

                QFrame#loginCard {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.05));
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 24px;
                    backdrop-filter: blur(20px);
                }

                QLabel#titleLabel {
                    color: #ffffff;
                    font-weight: 700;
                    letter-spacing: -1px;
                }

                QLabel#subtitleLabel {
                    color: rgba(255, 255, 255, 0.7);
                    font-weight: 400;
                }

                QLabel#fieldLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-weight: 500;
                }
            """)
            self._apply_starry_input_styles()
        else:
            # 现代化粉色主题
            self.setStyleSheet("""
                LoginWindow {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffecd2, stop:0.5 #fcb69f, stop:1 #ff9a9e);
                }

                QFrame#loginCard {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.8),
                        stop:1 rgba(255, 255, 255, 0.6));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 24px;
                    backdrop-filter: blur(20px);
                }

                QLabel#titleLabel {
                    color: #2d3748;
                    font-weight: 700;
                    letter-spacing: -1px;
                }

                QLabel#subtitleLabel {
                    color: rgba(45, 55, 72, 0.7);
                    font-weight: 400;
                }

                QLabel#fieldLabel {
                    color: rgba(45, 55, 72, 0.8);
                    font-weight: 500;
                }
            """)
            self._apply_kitty_input_styles()

    def _apply_starry_input_styles(self):
        """应用星空主题的输入框样式"""
        input_style = """
            QLineEdit#modernInput {
                padding: 16px 20px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.1);
                font-size: 15px;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-weight: 400;
            }
            QLineEdit#modernInput:focus {
                border-color: rgba(102, 126, 234, 0.8);
                background: rgba(255, 255, 255, 0.15);
                outline: none;
            }
            QLineEdit#modernInput::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }
        """

        button_style = """
            QPushButton#primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: #ffffff;
                border: none;
                border-radius: 16px;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            QPushButton#primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5a6fd8, stop:1 #634298);
                transform: translateY(-1px);
            }
            QPushButton#primaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4e5bc6, stop:1 #503a86);
            }
            QPushButton#primaryButton:disabled {
                background: rgba(255, 255, 255, 0.2);
                color: rgba(255, 255, 255, 0.5);
            }
        """

        link_style = """
            QLabel#linkText {
                color: rgba(255, 255, 255, 0.7);
            }
            QPushButton#linkButton {
                color: #667eea;
                border: none;
                background: transparent;
                text-decoration: underline;
                font-weight: 600;
            }
            QPushButton#linkButton:hover {
                color: #5a6fd8;
            }
        """

        theme_style = """
            QPushButton#themeButton {
                color: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 18px;
                padding: 8px 16px;
                background: rgba(255, 255, 255, 0.1);
                font-weight: 500;
            }
            QPushButton#themeButton:hover {
                background: rgba(255, 255, 255, 0.15);
                color: #ffffff;
                border-color: rgba(255, 255, 255, 0.3);
            }
        """

        version_style = """
            QLabel#versionLabel {
                color: rgba(255, 255, 255, 0.5);
            }
        """

        self.phone_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        self.login_btn.setStyleSheet(button_style)
        self.register_btn.setStyleSheet(link_style)
        self.theme_btn.setStyleSheet(theme_style)
        self.findChild(QLabel, "versionLabel").setStyleSheet(version_style) if self.findChild(QLabel, "versionLabel") else None

    def _setup_animations(self):
        """设置动画效果"""
        # 卡片入场动画
        self.card_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.card_animation.setDuration(800)
        self.card_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 淡入动画
        self.fade_animation = QPropertyAnimation(self.card_frame, b"windowOpacity")
        self.fade_animation.setDuration(600)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutQuart)

        # 启动入场动画
        QTimer.singleShot(100, self._start_entrance_animation)

    def _start_entrance_animation(self):
        """启动入场动画"""
        # 获取最终位置
        final_rect = self.card_frame.geometry()

        # 设置初始位置（从上方滑入）
        start_rect = QRect(final_rect.x(), final_rect.y() - 50, final_rect.width(), final_rect.height())
        self.card_frame.setGeometry(start_rect)

        # 设置动画
        self.card_animation.setStartValue(start_rect)
        self.card_animation.setEndValue(final_rect)

        # 启动动画
        self.card_animation.start()

    def _create_focus_handler(self, widget, is_focus_in):
        """创建焦点事件处理器"""
        original_handler = widget.focusInEvent if is_focus_in else widget.focusOutEvent

        def handler(event):
            # 调用原始处理器
            if hasattr(widget.__class__, 'focusInEvent' if is_focus_in else 'focusOutEvent'):
                original_handler(event)

            # 添加自定义动画效果
            self._animate_input_focus(widget, is_focus_in)

        return handler

    def _animate_input_focus(self, widget, focused):
        """输入框焦点动画"""
        # 这里可以添加更多的焦点动画效果
        # 例如：边框颜色变化、阴影效果等
        pass

    def _shake_animation(self):
        """错误时的摇摆动画"""
        self.shake_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.shake_animation.setDuration(500)
        self.shake_animation.setEasingCurve(QEasingCurve.Type.OutBounce)

        original_rect = self.card_frame.geometry()

        # 创建摇摆关键帧
        self.shake_animation.setKeyValueAt(0, original_rect)
        self.shake_animation.setKeyValueAt(0.2, QRect(original_rect.x() + 10, original_rect.y(), original_rect.width(), original_rect.height()))
        self.shake_animation.setKeyValueAt(0.4, QRect(original_rect.x() - 10, original_rect.y(), original_rect.width(), original_rect.height()))
        self.shake_animation.setKeyValueAt(0.6, QRect(original_rect.x() + 5, original_rect.y(), original_rect.width(), original_rect.height()))
        self.shake_animation.setKeyValueAt(0.8, QRect(original_rect.x() - 5, original_rect.y(), original_rect.width(), original_rect.height()))
        self.shake_animation.setKeyValueAt(1, original_rect)

        self.shake_animation.start()

    def _start_loading_animation(self):
        """开始加载动画"""
        # 简单的文字变化动画
        self.loading_timer = QTimer()
        self.loading_dots = 0
        self.loading_timer.timeout.connect(self._update_loading_text)
        self.loading_timer.start(500)

    def _stop_loading_animation(self):
        """停止加载动画"""
        if hasattr(self, 'loading_timer'):
            self.loading_timer.stop()

    def _update_loading_text(self):
        """更新加载文字"""
        dots = "." * (self.loading_dots % 4)
        self.login_btn.setText(f"Signing In{dots}")
        self.loading_dots += 1

    def _success_animation(self):
        """成功动画"""
        # 简单的缩放动画
        self.success_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.success_animation.setDuration(300)
        self.success_animation.setEasingCurve(QEasingCurve.Type.OutBack)

        original_rect = self.card_frame.geometry()
        scaled_rect = QRect(
            original_rect.x() - 5,
            original_rect.y() - 5,
            original_rect.width() + 10,
            original_rect.height() + 10
        )

        self.success_animation.setStartValue(original_rect)
        self.success_animation.setEndValue(scaled_rect)

        # 动画完成后恢复原始大小
        self.success_animation.finished.connect(lambda: self._restore_card_size(original_rect))
        self.success_animation.start()

    def _restore_card_size(self, original_rect):
        """恢复卡片原始大小"""
        self.restore_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.restore_animation.setDuration(200)
        self.restore_animation.setEasingCurve(QEasingCurve.Type.OutQuart)
        self.restore_animation.setStartValue(self.card_frame.geometry())
        self.restore_animation.setEndValue(original_rect)
        self.restore_animation.start()

    def _apply_kitty_input_styles(self):
        """应用Hello Kitty主题的输入框样式"""
        input_style = """
            QLineEdit#modernInput {
                padding: 16px 20px;
                border: 2px solid rgba(255, 192, 203, 0.4);
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.9);
                font-size: 15px;
                color: #2d3748;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-weight: 400;
            }
            QLineEdit#modernInput:focus {
                border-color: rgba(255, 154, 158, 0.8);
                background: rgba(255, 255, 255, 1.0);
                outline: none;
            }
            QLineEdit#modernInput::placeholder {
                color: rgba(45, 55, 72, 0.5);
            }
        """

        button_style = """
            QPushButton#primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff9a9e, stop:1 #fecfef);
                color: #2d3748;
                border: none;
                border-radius: 16px;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            QPushButton#primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff8a8e, stop:1 #febfdf);
                transform: translateY(-1px);
            }
            QPushButton#primaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff7a7e, stop:1 #feafcf);
            }
            QPushButton#primaryButton:disabled {
                background: rgba(255, 192, 203, 0.3);
                color: rgba(45, 55, 72, 0.5);
            }
        """

        link_style = """
            QLabel#linkText {
                color: rgba(45, 55, 72, 0.7);
            }
            QPushButton#linkButton {
                color: #ff9a9e;
                border: none;
                background: transparent;
                text-decoration: underline;
                font-weight: 600;
            }
            QPushButton#linkButton:hover {
                color: #ff8a8e;
            }
        """

        theme_style = """
            QPushButton#themeButton {
                color: rgba(45, 55, 72, 0.8);
                border: 1px solid rgba(255, 192, 203, 0.4);
                border-radius: 18px;
                padding: 8px 16px;
                background: rgba(255, 255, 255, 0.7);
                font-weight: 500;
            }
            QPushButton#themeButton:hover {
                background: rgba(255, 255, 255, 0.9);
                color: #2d3748;
                border-color: rgba(255, 192, 203, 0.6);
            }
        """

        version_style = """
            QLabel#versionLabel {
                color: rgba(45, 55, 72, 0.5);
            }
        """

        self.phone_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        self.login_btn.setStyleSheet(button_style)
        self.register_btn.setStyleSheet(link_style)
        self.theme_btn.setStyleSheet(theme_style)
        self.findChild(QLabel, "versionLabel").setStyleSheet(version_style) if self.findChild(QLabel, "versionLabel") else None

    def handle_login(self):
        """处理登录"""
        phone = self.phone_input.text().strip()
        password = self.password_input.text().strip()

        if not phone or not password:
            self.show_message("Please fill in all fields", "error")
            self._shake_animation()
            return

        # 禁用登录按钮并显示加载状态
        self.login_btn.setEnabled(False)
        self.login_btn.setText("Signing In...")

        # 添加加载动画效果
        self._start_loading_animation()

        # 模拟网络延迟
        QTimer.singleShot(1500, self._complete_login)

    def _complete_login(self):
        """完成登录处理"""
        phone = self.phone_input.text().strip()
        password = self.password_input.text().strip()

        # 执行登录
        success, message, user_data = self.auth_manager.login(phone, password)

        # 停止加载动画
        self._stop_loading_animation()

        if success:
            self.show_message("Welcome back! 🎉", "success")
            # 成功动画
            self._success_animation()
            # 延迟后关闭窗口
            QTimer.singleShot(2000, self.close)
        else:
            self.show_message(message, "error")
            self._shake_animation()

        # 恢复登录按钮
        self.login_btn.setEnabled(True)
        self.login_btn.setText("Sign In")

    def show_register(self):
        """显示注册窗口"""
        from src.ui.register_window import RegisterWindow

        self.register_window = RegisterWindow(self.auth_manager, self.theme_manager)
        self.register_window.show()
        self.hide()

        # 监听注册窗口关闭事件
        self.register_window.finished.connect(self.show)

    def show_message(self, text, msg_type):
        """显示现代化消息"""
        self.message_label.setText(text)

        if msg_type == "success":
            self.message_label.setStyleSheet("""
                QLabel#messageLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(76, 175, 80, 0.2),
                        stop:1 rgba(76, 175, 80, 0.1));
                    border: 1px solid rgba(76, 175, 80, 0.3);
                    color: #4caf50;
                    padding: 16px 20px;
                    border-radius: 12px;
                    font-weight: 500;
                    font-size: 14px;
                }
            """)
        else:
            self.message_label.setStyleSheet("""
                QLabel#messageLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(244, 67, 54, 0.2),
                        stop:1 rgba(244, 67, 54, 0.1));
                    border: 1px solid rgba(244, 67, 54, 0.3);
                    color: #f44336;
                    padding: 16px 20px;
                    border-radius: 12px;
                    font-weight: 500;
                    font-size: 14px;
                }
            """)

        self.message_label.show()

        # 3秒后隐藏消息
        QTimer.singleShot(3000, self.message_label.hide)

    def toggle_theme(self):
        """切换主题"""
        current = self.theme_manager.get_current_theme()
        new_theme = 'kitty' if current == 'starry' else 'starry'

        # 添加主题切换动画
        self._animate_theme_switch()

        # 延迟切换主题以配合动画
        QTimer.singleShot(150, lambda: self._complete_theme_switch(new_theme))

    def _animate_theme_switch(self):
        """主题切换动画"""
        self.theme_switch_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.theme_switch_animation.setDuration(300)
        self.theme_switch_animation.setEasingCurve(QEasingCurve.Type.InOutQuart)

        original_rect = self.card_frame.geometry()
        shrink_rect = QRect(
            original_rect.x() + 10,
            original_rect.y() + 10,
            original_rect.width() - 20,
            original_rect.height() - 20
        )

        # 先缩小
        self.theme_switch_animation.setStartValue(original_rect)
        self.theme_switch_animation.setEndValue(shrink_rect)
        self.theme_switch_animation.finished.connect(lambda: self._expand_after_theme_switch(original_rect))
        self.theme_switch_animation.start()

    def _expand_after_theme_switch(self, original_rect):
        """主题切换后展开"""
        self.theme_expand_animation = QPropertyAnimation(self.card_frame, b"geometry")
        self.theme_expand_animation.setDuration(300)
        self.theme_expand_animation.setEasingCurve(QEasingCurve.Type.OutBack)
        self.theme_expand_animation.setStartValue(self.card_frame.geometry())
        self.theme_expand_animation.setEndValue(original_rect)
        self.theme_expand_animation.start()

    def _complete_theme_switch(self, new_theme):
        """完成主题切换"""
        self.theme_manager.set_theme(new_theme)

        # 更新按钮文字
        if new_theme == 'starry':
            self.theme_btn.setText("🌙 Switch Theme")
        else:
            self.theme_btn.setText("⭐ Switch Theme")

    def closeEvent(self, event):
        """窗口关闭事件"""
        event.accept()