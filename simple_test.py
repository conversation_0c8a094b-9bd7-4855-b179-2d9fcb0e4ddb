#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单项目框架测试
"""

import sys
import os

def main():
    print("截图工具项目框架测试")
    print("=" * 40)
    
    # 检查项目结构
    required_dirs = [
        'src',
        'src/core', 
        'src/ui',
        'src/themes',
        'config',
        'data'
    ]
    
    required_files = [
        'main.py',
        'requirements.txt',
        'src/app.py',
        'src/core/auth_manager.py',
        'src/core/config_manager.py',
        'src/themes/theme_manager.py',
        'src/ui/login_window.py',
        'src/ui/register_window.py'
    ]
    
    print("检查目录结构...")
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path} (缺失)")
            
    print("\n检查核心文件...")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺失)")
            
    # 测试Python基础功能
    print("\n测试核心功能...")
    
    # 测试JSON操作
    import json
    test_data = {'theme': 'starry', 'test': True}
    json_str = json.dumps(test_data, ensure_ascii=False)
    parsed = json.loads(json_str)
    print(f"✓ JSON操作: {parsed}")
    
    # 测试文件操作
    os.makedirs('test_temp', exist_ok=True)
    with open('test_temp/test.txt', 'w', encoding='utf-8') as f:
        f.write('测试文件')
    
    with open('test_temp/test.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    print(f"✓ 文件操作: {content}")
    
    # 清理测试文件
    os.remove('test_temp/test.txt')
    os.rmdir('test_temp')
    
    # 测试加密功能
    import hashlib
    test_password = "123456"
    hashed = hashlib.sha256(test_password.encode('utf-8')).hexdigest()
    print(f"✓ 密码加密: {hashed[:10]}...")
    
    # 测试正则表达式
    import re
    phone_pattern = r'^1[3-9]\d{9}$'
    test_phone = "13800138000"
    is_valid = bool(re.match(phone_pattern, test_phone))
    print(f"✓ 手机号验证: {is_valid}")
    
    print("\n项目框架基础功能测试完成!")
    print("Python环境正常，核心模块结构完整")
    print("\n下一步: 安装PyQt6依赖")
    print("命令: pip install PyQt6")
    
    return True

if __name__ == "__main__":
    main()