#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目框架是否正常（无需PyQt6依赖）
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("测试项目结构...")
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    try:
        # 测试核心模块导入
        from src.core.auth_manager import AuthManager
        print("✅ AuthManager 导入成功")
        
        from src.core.config_manager import ConfigManager  
        print("✅ ConfigManager 导入成功")
        
        from src.themes.theme_manager import ThemeManager
        print("✅ ThemeManager 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_auth_manager():
    """测试认证管理器"""
    print("\n测试认证管理器...")
    
    try:
        # 由于没有PyQt6，我们需要创建一个简化版本
        class MockQObject:
            def __init__(self):
                pass
        
        # 临时替换QObject
        import src.core.auth_manager as auth_module
        original_qobject = getattr(auth_module, 'QObject', None)
        auth_module.QObject = MockQObject
        
        # 模拟信号
        class MockSignal:
            def emit(self, *args):
                pass
        auth_module.pyqtSignal = lambda *args: MockSignal()
        
        auth_manager = auth_module.AuthManager()
        
        # 测试注册
        success, message, _ = auth_manager.register("13800138000", "123456")
        print(f"注册测试: {success}, {message}")
        
        # 测试登录
        success, message, user_data = auth_manager.login("13800138000", "123456")
        print(f"登录测试: {success}, {message}")
        
        # 恢复原始QObject
        if original_qobject:
            auth_module.QObject = original_qobject
            
        return True
        
    except Exception as e:
        print(f"❌ 认证管理器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        # 模拟PyQt组件
        import src.core.config_manager as config_module
        
        class MockQObject:
            def __init__(self):
                pass
        class MockSignal:
            def emit(self, *args):
                pass
        
        config_module.QObject = MockQObject
        config_module.pyqtSignal = lambda *args: MockSignal()
        
        config_manager = config_module.ConfigManager()
        config_manager.load_config()
        
        # 测试配置读写
        config_manager.set('test_key', 'test_value')
        value = config_manager.get('test_key')
        print(f"配置读写测试: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_theme_manager():
    """测试主题管理器"""
    print("\n测试主题管理器...")
    
    try:
        import src.themes.theme_manager as theme_module
        
        class MockQObject:
            def __init__(self):
                pass
        class MockSignal:
            def emit(self, *args):
                pass
        
        theme_module.QObject = MockQObject
        theme_module.pyqtSignal = lambda *args: MockSignal()
        
        theme_manager = theme_module.ThemeManager()
        
        # 测试主题切换
        themes = theme_manager.get_available_themes()
        print(f"可用主题: {themes}")
        
        theme_manager.set_theme('kitty')
        current = theme_manager.get_current_theme()
        print(f"当前主题: {current}")
        
        # 测试样式获取
        style = theme_manager.get_window_style()
        print(f"样式长度: {len(style)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 主题管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("截图工具项目框架测试")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_auth_manager, 
        test_config_manager,
        test_theme_manager
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
            
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 项目框架搭建成功！")
        print("\n下一步:")
        print("1. 安装PyQt6依赖: pip install PyQt6")
        print("2. 运行主程序: python main.py")
        return True
    else:
        print("❌ 项目框架存在问题，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)