import { defineStore } from 'pinia'
import { ref } from 'vue'

export type Theme = 'starry' | 'kitty'

export const useThemeStore = defineStore('theme', () => {
  const currentTheme = ref<Theme>('starry')

  function setTheme(theme: Theme) {
    currentTheme.value = theme
    localStorage.setItem('selectedTheme', theme)
  }

  function loadStoredTheme() {
    const stored = localStorage.getItem('selectedTheme') as Theme
    if (stored && (stored === 'starry' || stored === 'kitty')) {
      currentTheme.value = stored
    }
  }

  return {
    currentTheme,
    setTheme,
    loadStoredTheme
  }
})