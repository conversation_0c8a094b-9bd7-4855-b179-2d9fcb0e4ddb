# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述
这是一个免费的Windows截图工具项目，提供完整的截图、编辑、保存和分享功能。项目采用螺旋星空系列/粉色浪漫Hellokity主题风格，UI设计要求简洁清新。

## 开发规范

### 任务管理
- 所有开发任务和进度都需要记录在`todo.md`文件中
- 完成任务后需要将状态从❌改为✅
- 开始开发时将状态改为🔄
- 及时更新开发进度总览中的统计数据

### 项目架构要求
基于todo.md中的规划，项目需要包含以下核心模块：
1. **截图引擎** - 支持全屏、活动窗口、自定义区域截图
2. **图像编辑模块** - 包括基础标注工具、隐私保护工具、高级编辑功能
3. **UI界面** - 符合主题风格的简洁清新界面
4. **系统集成** - 全局热键、系统托盘、右键菜单
5. **保存/导出系统** - 支持多种图片格式，剪贴板操作
6. **配置管理** - 用户设置、主题切换、热键自定义
7. **高级功能** - 滚动截图、OCR识别、翻译功能（调用有道接口）

### 开发阶段
按照todo.md中的里程碑进行开发：
- **第一阶段**: 基础功能（框架搭建、基础截图、简单编辑、基本保存）
- **第二阶段**: 完善功能（高级编辑、系统集成、用户设置、历史记录）
- **第三阶段**: 高级特性（OCR、滚动截图、批量处理、插件系统）
- **第四阶段**: 优化发布（性能优化、用户体验、安装包、文档）

### 特殊要求
- UI设计需要和主题风格一致（螺旋星空/粉色浪漫Hellokity）
- 翻译功能需要调用有道的接口
- 支持Windows平台的系统级功能（热键、托盘、截图）
- 需要制作安装包用于发布

### 技术栈建议
由于是Windows截图工具，建议使用：
- 桌面应用框架（如Electron、Tauri、或原生C#/C++）
- 图像处理库
- Windows系统API集成
- 网络请求库（用于翻译功能）

### 开发注意事项
- 每次完成功能后需要更新todo.md文件
- 保持代码简洁，符合项目的简洁清新风格
- 确保截图功能的性能和稳定性
- 注意用户隐私保护（马赛克、模糊工具等）