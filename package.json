{"name": "screenshot-tool", "version": "1.0.0", "description": "免费的Windows截图工具，提供完整的截图、编辑、保存和分享功能", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "electron-builder --dir --config electron-builder.json", "dev:renderer": "vite", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "start": "electron .", "dist": "npm run build && electron-builder", "lint": "eslint src --ext .ts,.vue", "type-check": "vue-tsc --noEmit"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^12.0.0", "concurrently": "^8.0.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vue-tsc": "^1.0.0"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.4.0", "crypto-js": "^4.1.1"}, "keywords": ["screenshot", "image-editing", "desktop-app", "windows"], "author": "不会写代码的小冯", "license": "MIT"}