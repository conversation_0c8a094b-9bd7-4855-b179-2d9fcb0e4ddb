import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import CryptoJS from 'crypto-js'

export interface User {
  id: string
  phone: string
  createdAt: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string>('')

  const isLoggedIn = computed(() => !!user.value && !!token.value)

  // 模拟用户数据存储（实际项目中应该使用数据库）
  const users = ref<Map<string, any>>(new Map())

  // 注册功能
  async function register(phone: string, password: string): Promise<{ success: boolean; message: string }> {
    try {
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(phone)) {
        return { success: false, message: '请输入正确的手机号格式' }
      }

      // 验证密码强度
      if (password.length < 6) {
        return { success: false, message: '密码至少需要6位' }
      }

      // 检查手机号是否已注册
      if (users.value.has(phone)) {
        return { success: false, message: '该手机号已注册' }
      }

      // 加密密码
      const hashedPassword = CryptoJS.SHA256(password).toString()

      // 创建用户
      const newUser = {
        id: Date.now().toString(),
        phone,
        password: hashedPassword,
        createdAt: new Date().toISOString()
      }

      users.value.set(phone, newUser)
      
      // 保存到本地存储
      localStorage.setItem('users', JSON.stringify(Array.from(users.value.entries())))

      return { success: true, message: '注册成功' }
    } catch (error) {
      return { success: false, message: '注册失败，请重试' }
    }
  }

  // 登录功能
  async function login(phone: string, password: string): Promise<{ success: boolean; message: string }> {
    try {
      // 从本地存储加载用户数据
      const savedUsers = localStorage.getItem('users')
      if (savedUsers) {
        users.value = new Map(JSON.parse(savedUsers))
      }

      const userData = users.value.get(phone)
      if (!userData) {
        return { success: false, message: '用户不存在' }
      }

      // 验证密码
      const hashedPassword = CryptoJS.SHA256(password).toString()
      if (userData.password !== hashedPassword) {
        return { success: false, message: '密码错误' }
      }

      // 设置用户信息和令牌
      user.value = {
        id: userData.id,
        phone: userData.phone,
        createdAt: userData.createdAt
      }
      token.value = CryptoJS.SHA256(userData.id + Date.now()).toString()

      // 保存会话到本地存储
      localStorage.setItem('currentUser', JSON.stringify(user.value))
      localStorage.setItem('authToken', token.value)

      return { success: true, message: '登录成功' }
    } catch (error) {
      return { success: false, message: '登录失败，请重试' }
    }
  }

  // 登出功能
  function logout() {
    user.value = null
    token.value = ''
    localStorage.removeItem('currentUser')
    localStorage.removeItem('authToken')
  }

  // 检查本地存储的会话
  function checkStoredSession() {
    const storedUser = localStorage.getItem('currentUser')
    const storedToken = localStorage.getItem('authToken')
    
    if (storedUser && storedToken) {
      user.value = JSON.parse(storedUser)
      token.value = storedToken
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    register,
    login,
    logout,
    checkStoredSession
  }
})