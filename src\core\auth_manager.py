#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证管理器
"""

import json
import hashlib
import os
import re
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal


class AuthManager(QObject):
    """用户认证管理器"""
    
    # 信号定义
    login_success = pyqtSignal(dict)  # 登录成功信号
    user_logout = pyqtSignal()        # 登出信号
    
    def __init__(self):
        super().__init__()
        self.users_file = "data/users.json"
        self.current_user = None
        self.session_token = None
        
        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 加载用户数据
        self._load_users()
        
    def _load_users(self):
        """加载用户数据"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    self.users_data = json.load(f)
            else:
                self.users_data = {}
        except Exception as e:
            print(f"加载用户数据失败: {e}")
            self.users_data = {}
            
    def _save_users(self):
        """保存用户数据"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存用户数据失败: {e}")
            return False
            
    def _hash_password(self, password):
        """密码哈希"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
        
    def _validate_phone(self, phone):
        """验证手机号格式"""
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
        
    def register(self, phone, password):
        """用户注册"""
        try:
            # 验证手机号格式
            if not self._validate_phone(phone):
                return False, "请输入正确的手机号格式", None
                
            # 验证密码强度
            if len(password) < 6:
                return False, "密码至少需要6位", None
                
            # 检查手机号是否已注册
            if phone in self.users_data:
                return False, "该手机号已注册", None
                
            # 创建用户数据
            user_data = {
                'id': f"user_{len(self.users_data) + 1}_{int(datetime.now().timestamp())}",
                'phone': phone,
                'password': self._hash_password(password),
                'created_at': datetime.now().isoformat(),
                'last_login': None
            }
            
            # 保存用户数据
            self.users_data[phone] = user_data
            
            if self._save_users():
                return True, "注册成功", None
            else:
                return False, "注册失败，请重试", None
                
        except Exception as e:
            print(f"注册异常: {e}")
            return False, "注册失败，请重试", None
            
    def login(self, phone, password):
        """用户登录"""
        try:
            # 检查用户是否存在
            if phone not in self.users_data:
                return False, "用户不存在", None
                
            user_data = self.users_data[phone]
            
            # 验证密码
            hashed_password = self._hash_password(password)
            if user_data['password'] != hashed_password:
                return False, "密码错误", None
                
            # 更新最后登录时间
            user_data['last_login'] = datetime.now().isoformat()
            self._save_users()
            
            # 设置当前用户和会话
            self.current_user = {
                'id': user_data['id'],
                'phone': user_data['phone'],
                'created_at': user_data['created_at'],
                'last_login': user_data['last_login']
            }
            
            # 生成会话令牌
            token_data = f"{user_data['id']}_{datetime.now().timestamp()}"
            self.session_token = hashlib.md5(token_data.encode()).hexdigest()
            
            # 保存会话到本地
            self._save_session()
            
            # 发出登录成功信号
            self.login_success.emit(self.current_user)
            
            return True, "登录成功", self.current_user
            
        except Exception as e:
            print(f"登录异常: {e}")
            return False, "登录失败，请重试", None
            
    def logout(self):
        """用户登出"""
        self.current_user = None
        self.session_token = None
        
        # 清除保存的会话
        self._clear_session()
        
        # 发出登出信号
        self.user_logout.emit()
        
    def _save_session(self):
        """保存会话到本地"""
        try:
            session_data = {
                'user': self.current_user,
                'token': self.session_token,
                'timestamp': datetime.now().isoformat()
            }
            
            with open("data/session.json", 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存会话失败: {e}")
            
    def _clear_session(self):
        """清除保存的会话"""
        try:
            if os.path.exists("data/session.json"):
                os.remove("data/session.json")
        except Exception as e:
            print(f"清除会话失败: {e}")
            
    def load_session(self):
        """加载保存的会话"""
        try:
            if os.path.exists("data/session.json"):
                with open("data/session.json", 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                    
                self.current_user = session_data.get('user')
                self.session_token = session_data.get('token')
                
                if self.current_user and self.session_token:
                    # 发出登录成功信号
                    self.login_success.emit(self.current_user)
                    return True
                    
        except Exception as e:
            print(f"加载会话失败: {e}")
            
        return False
        
    def is_logged_in(self):
        """检查是否已登录"""
        return self.current_user is not None and self.session_token is not None
        
    def get_current_user(self):
        """获取当前用户信息"""
        return self.current_user