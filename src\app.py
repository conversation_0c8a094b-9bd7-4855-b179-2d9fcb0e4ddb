#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图工具主应用程序类
"""

from PyQt6.QtWidgets import QSystemTrayIcon, QMenu
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QIcon, QAction

from src.ui.login_window import LoginWindow
from src.ui.main_window import MainWindow
from src.core.auth_manager import AuthManager
from src.core.screenshot_engine import ScreenshotEngine
from src.core.config_manager import ConfigManager
from src.themes.theme_manager import ThemeManager


class ScreenshotApp(QObject):
    """截图工具主应用程序类"""
    
    # 信号定义
    user_logged_in = pyqtSignal(dict)  # 用户登录信号
    user_logged_out = pyqtSignal()     # 用户登出信号
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.theme_manager = ThemeManager()
        self.auth_manager = AuthManager()
        self.screenshot_engine = ScreenshotEngine()
        
        # 界面组件
        self.login_window = None
        self.main_window = None
        self.tray_icon = None
        
        # 初始化设置
        self._init_components()
        
    def _init_components(self):
        """初始化各个组件"""
        # 加载配置
        self.config_manager.load_config()
        
        # 加载主题
        saved_theme = self.config_manager.get('theme', 'starry')
        self.theme_manager.set_theme(saved_theme)
        
        # 连接信号
        self.auth_manager.login_success.connect(self._on_login_success)
        self.auth_manager.user_logout.connect(self._on_logout)
        
    def show_login(self):
        """显示登录界面"""
        if not self.login_window:
            self.login_window = LoginWindow(self.auth_manager, self.theme_manager)
            
        self.login_window.show()
        
    def show_main_window(self, user_data):
        """显示主界面"""
        if not self.main_window:
            self.main_window = MainWindow(
                self.auth_manager,
                self.screenshot_engine, 
                self.theme_manager,
                self.config_manager
            )
            
        # 隐藏登录界面
        if self.login_window:
            self.login_window.hide()
            
        # 显示主界面
        self.main_window.show()
        self.main_window.set_user_data(user_data)
        
        # 创建系统托盘
        self._create_tray_icon()
        
    def _create_tray_icon(self):
        """创建系统托盘图标"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
            
        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon()
        
        # 设置图标
        if hasattr(self, 'app_icon'):
            self.tray_icon.setIcon(self.app_icon)
        
        # 创建右键菜单
        tray_menu = QMenu()
        
        # 快速截图
        screenshot_action = QAction("快速截图 (Ctrl+Shift+A)", tray_menu)
        screenshot_action.triggered.connect(self.quick_screenshot)
        tray_menu.addAction(screenshot_action)
        
        tray_menu.addSeparator()
        
        # 显示主界面
        show_action = QAction("显示主界面", tray_menu)
        show_action.triggered.connect(self._show_main_window)
        tray_menu.addAction(show_action)
        
        # 设置
        settings_action = QAction("设置", tray_menu)
        settings_action.triggered.connect(self._show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", tray_menu)
        quit_action.triggered.connect(self._quit_app)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        
        # 托盘图标点击事件
        self.tray_icon.activated.connect(self._tray_icon_activated)
        
    def quick_screenshot(self):
        """快速截图"""
        if self.screenshot_engine:
            self.screenshot_engine.start_region_screenshot()
            
    def _show_main_window(self):
        """显示主界面"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
            
    def _show_settings(self):
        """显示设置界面"""
        if self.main_window:
            self.main_window.show_settings()
            
    def _quit_app(self):
        """退出应用程序"""
        if self.tray_icon:
            self.tray_icon.hide()
        if self.main_window:
            self.main_window.close()
        if self.login_window:
            self.login_window.close()
            
    def _tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self._show_main_window()
            
    def _on_login_success(self, user_data):
        """登录成功处理"""
        self.show_main_window(user_data)
        self.user_logged_in.emit(user_data)
        
    def _on_logout(self):
        """登出处理"""
        # 隐藏主界面
        if self.main_window:
            self.main_window.hide()
            
        # 隐藏托盘图标
        if self.tray_icon:
            self.tray_icon.hide()
            
        # 显示登录界面
        self.show_login()
        
        self.user_logged_out.emit()