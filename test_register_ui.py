#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化注册页面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from src.core.auth_manager import AuthManager
from src.themes.theme_manager import ThemeManager
from src.ui.register_window import RegisterWindow


class MockAuthManager:
    """模拟认证管理器"""
    
    def register(self, phone, password):
        """模拟注册"""
        if phone == "test":
            return False, "Phone number already exists", None
        return True, "Registration successful", {"phone": phone}


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建管理器
    auth_manager = MockAuthManager()
    theme_manager = ThemeManager()
    
    # 创建注册窗口
    register_window = RegisterWindow(auth_manager, theme_manager)
    register_window.show()
    
    # 自动切换主题演示
    def switch_theme():
        current = theme_manager.get_current_theme()
        if current == 'starry':
            theme_manager.set_theme('kitty')
        else:
            theme_manager.set_theme('starry')
        
        # 5秒后再次切换
        QTimer.singleShot(5000, switch_theme)
    
    # 5秒后开始自动切换主题
    QTimer.singleShot(5000, switch_theme)
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
