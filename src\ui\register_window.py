#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册窗口
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont


class RegisterWindow(QWidget):
    """注册窗口类"""
    
    finished = pyqtSignal()  # 窗口完成信号
    
    def __init__(self, auth_manager, theme_manager):
        super().__init__()
        self.auth_manager = auth_manager
        self.theme_manager = theme_manager
        
        self.init_ui()
        self.apply_theme()
        
        # 连接信号
        self.theme_manager.theme_changed.connect(self.apply_theme)
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("截图工具 - 注册")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Logo区域
        logo_frame = QFrame()
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Logo图标
        logo_label = QLabel("📸")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setStyleSheet("font-size: 60px;")
        
        # 标题
        title_label = QLabel("注册账号")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        # 副标题
        subtitle_label = QLabel("加入截图工具，开启便捷体验")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(12)
        subtitle_label.setFont(subtitle_font)
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(title_label)
        logo_layout.addWidget(subtitle_label)
        
        # 表单区域
        form_frame = QFrame()
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # 手机号输入
        phone_label = QLabel("手机号:")
        phone_label.setFont(QFont("", 11, QFont.Weight.Medium))
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入手机号")
        self.phone_input.setMaxLength(11)
        self.phone_input.setFixedHeight(45)
        self.phone_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                font-size: 15px;
                color: white;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QLineEdit:focus {
                border-color: rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.2);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        """)
        
        # 密码输入
        password_label = QLabel("密码:")
        password_label.setFont(QFont("", 11, QFont.Weight.Medium))
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码（至少6位）")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(45)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                font-size: 15px;
                color: white;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QLineEdit:focus {
                border-color: rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.2);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        """)
        
        # 确认密码输入
        confirm_label = QLabel("确认密码:")
        confirm_label.setFont(QFont("", 11, QFont.Weight.Medium))
        
        self.confirm_input = QLineEdit()
        self.confirm_input.setPlaceholderText("请再次输入密码")
        self.confirm_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_input.setFixedHeight(45)
        self.confirm_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                font-size: 15px;
                color: white;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QLineEdit:focus {
                border-color: rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.2);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        """)
        
        # 注册按钮
        self.register_btn = QPushButton("注册")
        self.register_btn.setFixedHeight(45)
        self.register_btn.setFont(QFont("", 12, QFont.Weight.Bold))
        self.register_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5a6fd8, stop:1 #634298);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4e5bc6, stop:1 #503a86);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.3);
                color: rgba(255, 255, 255, 0.6);
            }
        """)
        
        # 返回登录链接区域
        login_frame = QFrame()
        login_layout = QHBoxLayout(login_frame)
        login_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        login_text = QLabel("已有账号？")
        login_text.setFont(QFont("", 10))
        
        self.login_btn = QPushButton("立即登录")
        self.login_btn.setFlat(True)
        self.login_btn.setFont(QFont("", 10, QFont.Weight.Bold))
        self.login_btn.setStyleSheet("""
            QPushButton {
                color: #667eea;
                border: none;
                text-decoration: underline;
            }
            QPushButton:hover {
                color: #5a6fd8;
            }
        """)
        
        login_layout.addWidget(login_text)
        login_layout.addWidget(self.login_btn)
        
        # 添加控件到表单布局
        form_layout.addWidget(phone_label)
        form_layout.addWidget(self.phone_input)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(confirm_label)
        form_layout.addWidget(self.confirm_input)
        form_layout.addWidget(self.register_btn)
        form_layout.addWidget(login_frame)
        
        # 消息显示区域
        self.message_label = QLabel("")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setWordWrap(True)
        self.message_label.hide()
        
        # 添加到主布局
        main_layout.addWidget(logo_frame)
        main_layout.addWidget(form_frame)
        main_layout.addWidget(self.message_label)
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # 连接信号
        self.register_btn.clicked.connect(self.handle_register)
        self.login_btn.clicked.connect(self.show_login)
        self.confirm_input.returnPressed.connect(self.handle_register)
        
    def apply_theme(self):
        """应用主题"""
        current_theme = self.theme_manager.get_current_theme()
        
        if current_theme == 'starry':
            # 螺旋星空主题
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e3c72, stop:1 #2a5298);
                    color: white;
                }
                QLabel {
                    color: white;
                }
            """)
        else:
            # 粉色浪漫Hello Kitty主题
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ff9a9e, stop:0.5 #fecfef, stop:1 #fecfef);
                    color: #333333;
                }
                QLabel {
                    color: #333333;
                }
            """)
            
            # 更新输入框样式为粉色系
            kitty_input_style = """
                QLineEdit {
                    padding: 8px 16px;
                    border: 2px solid rgba(255, 192, 203, 0.5);
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.8);
                    font-size: 15px;
                    color: #333333;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                }
                QLineEdit:focus {
                    border-color: rgba(255, 192, 203, 0.8);
                    background: rgba(255, 255, 255, 0.9);
                }
                QLineEdit::placeholder {
                    color: rgba(102, 102, 102, 0.8);
                }
            """
            self.phone_input.setStyleSheet(kitty_input_style)
            self.password_input.setStyleSheet(kitty_input_style)
            self.confirm_input.setStyleSheet(kitty_input_style)
            
            # 更新按钮样式为粉色系
            self.register_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ff9a9e, stop:1 #fecfef);
                    color: #333333;
                    border: none;
                    border-radius: 10px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ff8a8e, stop:1 #febfdf);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ff7a7e, stop:1 #feafcf);
                }
            """)
            
    def handle_register(self):
        """处理注册"""
        phone = self.phone_input.text().strip()
        password = self.password_input.text().strip()
        confirm_password = self.confirm_input.text().strip()
        
        if not phone or not password or not confirm_password:
            self.show_message("请填写完整信息", "error")
            return
            
        if password != confirm_password:
            self.show_message("两次输入的密码不一致", "error")
            return
            
        # 禁用注册按钮
        self.register_btn.setEnabled(False)
        self.register_btn.setText("注册中...")
        
        # 执行注册
        success, message, _ = self.auth_manager.register(phone, password)
        
        if success:
            self.show_message(message, "success")
            # 延迟后自动跳转到登录
            QTimer.singleShot(1500, self.show_login)
        else:
            self.show_message(message, "error")
            
        # 恢复注册按钮
        self.register_btn.setEnabled(True)
        self.register_btn.setText("注册")
        
    def show_login(self):
        """显示登录窗口"""
        self.finished.emit()
        self.close()
        
    def show_message(self, text, msg_type):
        """显示消息"""
        self.message_label.setText(text)
        
        if msg_type == "success":
            self.message_label.setStyleSheet("""
                QLabel {
                    background: rgba(76, 175, 80, 0.2);
                    border: 1px solid rgba(76, 175, 80, 0.3);
                    color: #4caf50;
                    padding: 10px;
                    border-radius: 5px;
                }
            """)
        else:
            self.message_label.setStyleSheet("""
                QLabel {
                    background: rgba(244, 67, 54, 0.2);
                    border: 1px solid rgba(244, 67, 54, 0.3);
                    color: #f44336;
                    padding: 10px;
                    border-radius: 5px;
                }
            """)
            
        self.message_label.show()
        
        # 3秒后隐藏消息
        QTimer.singleShot(3000, self.message_label.hide)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.finished.emit()
        event.accept()