#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化注册窗口 - 采用毛玻璃效果和现代设计语言
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QFrame, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QColor


class RegisterWindow(QWidget):
    """现代化注册窗口类"""

    finished = pyqtSignal()  # 窗口完成信号

    def __init__(self, auth_manager, theme_manager):
        super().__init__()
        self.auth_manager = auth_manager
        self.theme_manager = theme_manager

        self.init_ui()
        self.apply_theme()

        # 连接信号
        self.theme_manager.theme_changed.connect(self.apply_theme)

    def init_ui(self):
        """初始化现代化UI"""
        self.setWindowTitle("ScreenShot Pro - Sign Up")
        self.setFixedSize(480, 720)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)

        # 主容器布局 - 填满整个窗口
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建主容器 - 填满整个窗口
        self.main_frame = QFrame()
        self.main_frame.setObjectName("registerCard")
        card_layout = QVBoxLayout(self.main_frame)
        card_layout.setSpacing(32)
        card_layout.setContentsMargins(48, 48, 48, 48)

        # Logo和标题区域
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.setSpacing(16)

        # 现代化Logo图标
        logo_label = QLabel("🎯")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setStyleSheet("""
            font-size: 72px;
            margin-bottom: 8px;
        """)

        # 主标题
        title_label = QLabel("Create Account")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("titleLabel")
        title_font = QFont("Segoe UI", 28, QFont.Weight.Bold)
        title_label.setFont(title_font)

        # 副标题
        subtitle_label = QLabel("Join ScreenShot Pro for Amazing Experience")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_font = QFont("Segoe UI", 13)
        subtitle_label.setFont(subtitle_font)

        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)

        # 表单区域
        form_frame = QFrame()
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(24)

        # 输入字段容器
        inputs_frame = QFrame()
        inputs_layout = QVBoxLayout(inputs_frame)
        inputs_layout.setSpacing(20)

        # 手机号输入组
        phone_container = QFrame()
        phone_container_layout = QVBoxLayout(phone_container)
        phone_container_layout.setSpacing(8)

        phone_label = QLabel("📱 Phone Number")
        phone_label.setObjectName("fieldLabel")
        phone_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("Enter your phone number")
        self.phone_input.setMaxLength(11)
        self.phone_input.setFixedHeight(56)
        self.phone_input.setObjectName("modernInput")

        phone_container_layout.addWidget(phone_label)
        phone_container_layout.addWidget(self.phone_input)

        # 密码输入组
        password_container = QFrame()
        password_container_layout = QVBoxLayout(password_container)
        password_container_layout.setSpacing(8)

        password_label = QLabel("🔒 Password")
        password_label.setObjectName("fieldLabel")
        password_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter password (at least 6 characters)")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedHeight(56)
        self.password_input.setObjectName("modernInput")

        password_container_layout.addWidget(password_label)
        password_container_layout.addWidget(self.password_input)

        # 确认密码输入组
        confirm_container = QFrame()
        confirm_container_layout = QVBoxLayout(confirm_container)
        confirm_container_layout.setSpacing(8)

        confirm_label = QLabel("🔐 Confirm Password")
        confirm_label.setObjectName("fieldLabel")
        confirm_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))

        self.confirm_input = QLineEdit()
        self.confirm_input.setPlaceholderText("Confirm your password")
        self.confirm_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_input.setFixedHeight(56)
        self.confirm_input.setObjectName("modernInput")

        confirm_container_layout.addWidget(confirm_label)
        confirm_container_layout.addWidget(self.confirm_input)

        inputs_layout.addWidget(phone_container)
        inputs_layout.addWidget(password_container)
        inputs_layout.addWidget(confirm_container)

        # 注册按钮
        self.register_btn = QPushButton("Create Account")
        self.register_btn.setFixedHeight(56)
        self.register_btn.setObjectName("primaryButton")
        self.register_btn.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))

        # 返回登录链接区域
        login_frame = QFrame()
        login_layout = QHBoxLayout(login_frame)
        login_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        login_layout.setSpacing(8)

        login_text = QLabel("Already have an account?")
        login_text.setObjectName("linkText")
        login_text.setFont(QFont("Segoe UI", 11))

        self.login_btn = QPushButton("Sign In")
        self.login_btn.setFlat(True)
        self.login_btn.setObjectName("linkButton")
        self.login_btn.setFont(QFont("Segoe UI", 11, QFont.Weight.Bold))

        login_layout.addWidget(login_text)
        login_layout.addWidget(self.login_btn)

        # 添加控件到表单布局
        form_layout.addWidget(inputs_frame)
        form_layout.addWidget(self.register_btn)
        form_layout.addWidget(login_frame)

        # 底部区域
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setSpacing(16)
        footer_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 版本信息
        version_label = QLabel("v1.0.0 • Join the Community")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setObjectName("versionLabel")
        version_label.setFont(QFont("Segoe UI", 9))

        footer_layout.addWidget(version_label)

        # 消息显示区域
        self.message_label = QLabel("")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setWordWrap(True)
        self.message_label.setObjectName("messageLabel")
        self.message_label.hide()

        # 添加到卡片布局
        card_layout.addWidget(header_frame)
        card_layout.addWidget(form_frame)
        card_layout.addWidget(footer_frame)
        card_layout.addWidget(self.message_label)

        # 主容器填满整个窗口
        main_layout.addWidget(self.main_frame)

        self.setLayout(main_layout)

        # 连接信号
        self.register_btn.clicked.connect(self.handle_register)
        self.login_btn.clicked.connect(self.show_login)
        self.confirm_input.returnPressed.connect(self.handle_register)

        # 初始化动画
        self._setup_animations()
        
    def apply_theme(self):
        """应用现代化主题"""
        current_theme = self.theme_manager.get_current_theme()

        if current_theme == 'starry':
            # 现代化星空主题 - 填满整个窗口
            self.setStyleSheet("""
                RegisterWindow {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0f0c29, stop:0.5 #302b63, stop:1 #24243e);
                }

                QFrame#registerCard {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0f0c29, stop:0.5 #302b63, stop:1 #24243e);
                    border: none;
                    border-radius: 0px;
                }

                QLabel#titleLabel {
                    color: #ffffff;
                    font-weight: 700;
                    letter-spacing: -1px;
                }

                QLabel#subtitleLabel {
                    color: rgba(255, 255, 255, 0.7);
                    font-weight: 400;
                }

                QLabel#fieldLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-weight: 500;
                }
            """)
            self._apply_starry_input_styles()
        else:
            # 现代化粉色主题 - 填满整个窗口
            self.setStyleSheet("""
                RegisterWindow {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffecd2, stop:0.5 #fcb69f, stop:1 #ff9a9e);
                }

                QFrame#registerCard {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffecd2, stop:0.5 #fcb69f, stop:1 #ff9a9e);
                    border: none;
                    border-radius: 0px;
                }

                QLabel#titleLabel {
                    color: #2d3748;
                    font-weight: 700;
                    letter-spacing: -1px;
                }

                QLabel#subtitleLabel {
                    color: rgba(45, 55, 72, 0.7);
                    font-weight: 400;
                }

                QLabel#fieldLabel {
                    color: rgba(45, 55, 72, 0.9);
                    font-weight: 600;
                }
            """)
            self._apply_kitty_input_styles()

    def _setup_animations(self):
        """设置动画效果"""
        # 入场动画
        self.entrance_animation = QPropertyAnimation(self.main_frame, b"geometry")
        self.entrance_animation.setDuration(800)
        self.entrance_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 启动入场动画
        QTimer.singleShot(100, self._start_entrance_animation)

    def _start_entrance_animation(self):
        """启动入场动画"""
        # 获取最终位置
        final_rect = self.main_frame.geometry()

        # 设置初始位置（从上方滑入）
        start_rect = QRect(final_rect.x(), final_rect.y() - 50, final_rect.width(), final_rect.height())
        self.main_frame.setGeometry(start_rect)

        # 设置动画
        self.entrance_animation.setStartValue(start_rect)
        self.entrance_animation.setEndValue(final_rect)

        # 启动动画
        self.entrance_animation.start()

    def _apply_starry_input_styles(self):
        """应用星空主题的输入框样式"""
        input_style = """
            QLineEdit#modernInput {
                padding: 16px 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.15);
                font-size: 15px;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-weight: 400;
            }
            QLineEdit#modernInput:focus {
                border-color: rgba(102, 126, 234, 0.8);
                background: rgba(255, 255, 255, 0.2);
                outline: none;
            }
            QLineEdit#modernInput::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        """

        button_style = """
            QPushButton#primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: #ffffff;
                border: none;
                border-radius: 16px;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            QPushButton#primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5a6fd8, stop:1 #634298);
                transform: translateY(-1px);
            }
            QPushButton#primaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4e5bc6, stop:1 #503a86);
            }
            QPushButton#primaryButton:disabled {
                background: rgba(255, 255, 255, 0.2);
                color: rgba(255, 255, 255, 0.5);
            }
        """

        link_style = """
            QLabel#linkText {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
            }
            QPushButton#linkButton {
                color: #667eea;
                border: none;
                background: transparent;
                text-decoration: underline;
                font-weight: 600;
            }
            QPushButton#linkButton:hover {
                color: #5a6fd8;
            }
        """

        version_style = """
            QLabel#versionLabel {
                color: rgba(255, 255, 255, 0.7);
                font-weight: 400;
            }
        """

        self.phone_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        self.confirm_input.setStyleSheet(input_style)
        self.register_btn.setStyleSheet(button_style)
        self.login_btn.setStyleSheet(link_style)
        self.findChild(QLabel, "versionLabel").setStyleSheet(version_style) if self.findChild(QLabel, "versionLabel") else None

    def _apply_kitty_input_styles(self):
        """应用Hello Kitty主题的输入框样式"""
        input_style = """
            QLineEdit#modernInput {
                padding: 16px 20px;
                border: 2px solid rgba(255, 154, 158, 0.6);
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.95);
                font-size: 15px;
                color: #1a202c;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-weight: 500;
            }
            QLineEdit#modernInput:focus {
                border-color: rgba(255, 154, 158, 1.0);
                background: rgba(255, 255, 255, 1.0);
                outline: none;
                box-shadow: 0 0 0 3px rgba(255, 154, 158, 0.2);
            }
            QLineEdit#modernInput::placeholder {
                color: rgba(45, 55, 72, 0.8);
                font-weight: 400;
            }
        """

        button_style = """
            QPushButton#primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff9a9e, stop:1 #fecfef);
                color: #2d3748;
                border: none;
                border-radius: 16px;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            QPushButton#primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff8a8e, stop:1 #febfdf);
                transform: translateY(-1px);
            }
            QPushButton#primaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff7a7e, stop:1 #feafcf);
            }
            QPushButton#primaryButton:disabled {
                background: rgba(255, 192, 203, 0.3);
                color: rgba(45, 55, 72, 0.5);
            }
        """

        link_style = """
            QLabel#linkText {
                color: rgba(45, 55, 72, 0.8);
                font-weight: 500;
            }
            QPushButton#linkButton {
                color: #2d3748;
                border: none;
                background: transparent;
                text-decoration: underline;
                font-weight: 700;
                font-size: 11px;
            }
            QPushButton#linkButton:hover {
                color: #1a202c;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 2px 6px;
            }
        """

        version_style = """
            QLabel#versionLabel {
                color: rgba(45, 55, 72, 0.5);
            }
        """

        self.phone_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        self.confirm_input.setStyleSheet(input_style)
        self.register_btn.setStyleSheet(button_style)
        self.login_btn.setStyleSheet(link_style)
        self.findChild(QLabel, "versionLabel").setStyleSheet(version_style) if self.findChild(QLabel, "versionLabel") else None

    def handle_register(self):
        """处理注册"""
        phone = self.phone_input.text().strip()
        password = self.password_input.text().strip()
        confirm_password = self.confirm_input.text().strip()

        if not phone or not password or not confirm_password:
            self.show_message("Please fill in all fields", "error")
            return

        if len(password) < 6:
            self.show_message("Password must be at least 6 characters", "error")
            return

        if password != confirm_password:
            self.show_message("Passwords do not match", "error")
            return

        # 禁用注册按钮
        self.register_btn.setEnabled(False)
        self.register_btn.setText("Creating Account...")

        # 执行注册
        success, message, _ = self.auth_manager.register(phone, password)

        if success:
            self.show_message("Account created successfully! Redirecting...", "success")
            # 延迟后自动跳转到登录
            QTimer.singleShot(1500, self.show_login)
        else:
            self.show_message(message, "error")

        # 恢复注册按钮
        self.register_btn.setEnabled(True)
        self.register_btn.setText("Create Account")
        
    def show_login(self):
        """显示登录窗口"""
        self.finished.emit()
        self.close()
        
    def show_message(self, text, msg_type):
        """显示现代化消息"""
        self.message_label.setText(text)

        current_theme = self.theme_manager.get_current_theme()

        if msg_type == "success":
            if current_theme == 'starry':
                self.message_label.setStyleSheet("""
                    QLabel#messageLabel {
                        background: rgba(76, 175, 80, 0.2);
                        border: 2px solid rgba(76, 175, 80, 0.4);
                        color: #81c784;
                        padding: 16px 20px;
                        border-radius: 12px;
                        font-weight: 500;
                        font-size: 13px;
                    }
                """)
            else:
                self.message_label.setStyleSheet("""
                    QLabel#messageLabel {
                        background: rgba(76, 175, 80, 0.15);
                        border: 2px solid rgba(76, 175, 80, 0.3);
                        color: #2e7d32;
                        padding: 16px 20px;
                        border-radius: 12px;
                        font-weight: 500;
                        font-size: 13px;
                    }
                """)
        else:
            if current_theme == 'starry':
                self.message_label.setStyleSheet("""
                    QLabel#messageLabel {
                        background: rgba(244, 67, 54, 0.2);
                        border: 2px solid rgba(244, 67, 54, 0.4);
                        color: #e57373;
                        padding: 16px 20px;
                        border-radius: 12px;
                        font-weight: 500;
                        font-size: 13px;
                    }
                """)
            else:
                self.message_label.setStyleSheet("""
                    QLabel#messageLabel {
                        background: rgba(244, 67, 54, 0.15);
                        border: 2px solid rgba(244, 67, 54, 0.3);
                        color: #c62828;
                        padding: 16px 20px;
                        border-radius: 12px;
                        font-weight: 500;
                        font-size: 13px;
                    }
                """)

        self.message_label.show()

        # 4秒后隐藏消息
        QTimer.singleShot(4000, self.message_label.hide)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.finished.emit()
        event.accept()