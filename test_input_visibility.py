#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输入框可见性修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from src.core.auth_manager import AuthManager
from src.themes.theme_manager import ThemeManager
from src.ui.login_window import LoginWindow
from src.ui.register_window import RegisterWindow


class MockAuthManager:
    """模拟认证管理器"""
    
    def login(self, phone, password):
        return False, "Demo mode - login disabled", None
    
    def register(self, phone, password):
        return False, "Demo mode - register disabled", None


def main():
    """测试输入框可见性"""
    print("🔍 Testing Input Field Visibility Fix...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("Input Visibility Test")
    
    # 创建管理器
    auth_manager = MockAuthManager()
    theme_manager = ThemeManager()
    
    # 设置为粉色主题
    theme_manager.set_theme('kitty')
    
    # 创建登录窗口
    login_window = LoginWindow(auth_manager, theme_manager)
    login_window.show()
    
    print("✅ Login window created with Pink theme")
    print("🎨 Input field improvements:")
    print("   • Darker placeholder text (0.5 → 0.8 opacity)")
    print("   • Stronger border color")
    print("   • Higher background opacity (0.9 → 0.95)")
    print("   • Darker text color (#2d3748 → #1a202c)")
    print("   • Enhanced focus effects with shadow")
    print("   • Bolder field labels (500 → 600 weight)")
    
    # 3秒后显示注册窗口
    def show_register():
        register_window = RegisterWindow(auth_manager, theme_manager)
        register_window.show()
        print("✅ Register window created with same improvements")
    
    QTimer.singleShot(3000, show_register)
    
    # 提示用户测试
    print("\n📝 Test Instructions:")
    print("1. Check placeholder text visibility in input fields")
    print("2. Try typing in the fields to see text clarity")
    print("3. Focus on fields to see enhanced border effects")
    print("4. Register window will appear in 3 seconds")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
