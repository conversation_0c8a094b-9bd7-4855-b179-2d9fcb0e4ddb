#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化UI界面
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.ui.login_window import LoginWindow
from src.core.auth_manager import AuthManager
from src.themes.theme_manager import ThemeManager

def main():
    """测试现代化登录界面"""
    print("Testing Modern UI Design...")
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("ScreenShot Pro")
    app.setApplicationVersion("1.0.0")
    
    # 支持高DPI显示
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
    except AttributeError:
        pass
    
    # 创建管理器
    auth_manager = AuthManager()
    theme_manager = ThemeManager()
    
    # 创建登录窗口
    login_window = LoginWindow(auth_manager, theme_manager)
    
    # 显示窗口
    login_window.show()
    
    print("Modern login window displayed!")
    print("Features:")
    print("✓ Glassmorphism effect")
    print("✓ Modern gradients")
    print("✓ Rounded corners")
    print("✓ Shadow effects")
    print("✓ Responsive design")
    print("✓ Theme switching")
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
