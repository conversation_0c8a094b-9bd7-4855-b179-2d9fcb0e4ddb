#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口（占位文件，后续实现）
"""

from PyQt6.QtWidgets import QMainWindow, QLabel
from PyQt6.QtCore import Qt


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, auth_manager, screenshot_engine, theme_manager, config_manager):
        super().__init__()
        self.auth_manager = auth_manager
        self.screenshot_engine = screenshot_engine
        self.theme_manager = theme_manager
        self.config_manager = config_manager
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("截图工具 - 主界面")
        self.setGeometry(100, 100, 800, 600)
        
        # 临时显示标签
        label = QLabel("主界面 - 开发中...")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setCentralWidget(label)
        
    def set_user_data(self, user_data):
        """设置用户数据"""
        self.user_data = user_data
        self.setWindowTitle(f"截图工具 - 欢迎 {user_data['phone']}")
        
    def show_settings(self):
        """显示设置界面"""
        print("显示设置界面...")