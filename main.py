#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图工具主程序
Author: 不会写代码的小冯
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
from src.app import ScreenshotApp

def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("截图工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("不会写代码的小冯")
    
    # 支持高DPI显示（PyQt6中已默认启用）
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
    except AttributeError:
        # PyQt6中这些属性已经默认启用
        pass
    
    # 设置应用程序图标
    if os.path.exists("assets/icon.png"):
        app.setWindowIcon(QIcon("assets/icon.png"))
    
    # 创建主应用程序
    screenshot_app = ScreenshotApp()
    
    # 显示启动界面（登录界面）
    screenshot_app.show_login()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()