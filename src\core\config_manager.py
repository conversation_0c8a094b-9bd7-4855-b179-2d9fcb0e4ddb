#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
"""

import json
import os
from PyQt6.QtCore import QObject, pyqtSignal


class ConfigManager(QObject):
    """配置管理器"""
    
    # 信号定义
    config_changed = pyqtSignal(str, object)  # 配置变更信号
    
    def __init__(self):
        super().__init__()
        self.config_file = "config/settings.json"
        self.config_data = {}
        
        # 确保配置目录存在
        os.makedirs("config", exist_ok=True)
        
        # 默认配置
        self.default_config = {
            'theme': 'starry',
            'auto_save': True,
            'save_path': os.path.expanduser("~/Pictures/Screenshots"),
            'hotkey': 'ctrl+shift+a',
            'image_quality': 95,
            'auto_copy_to_clipboard': True,
            'show_notification': True,
            'minimize_to_tray': True,
            'startup_with_system': False,
            'language': 'zh_CN'
        }
        
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                self.config_data = self.default_config.copy()
                self.save_config()
        except Exception as e:
            print(f"加载配置失败: {e}")
            self.config_data = self.default_config.copy()
            
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
            
    def get(self, key, default_value=None):
        """获取配置值"""
        return self.config_data.get(key, default_value)
        
    def set(self, key, value):
        """设置配置值"""
        self.config_data[key] = value
        self.save_config()
        self.config_changed.emit(key, value)
        
    def get_all(self):
        """获取所有配置"""
        return self.config_data.copy()
        
    def reset_to_default(self):
        """重置为默认配置"""
        self.config_data = self.default_config.copy()
        self.save_config()