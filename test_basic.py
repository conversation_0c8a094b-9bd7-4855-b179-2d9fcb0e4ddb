# -*- coding: utf-8 -*-
"""
基础项目框架测试
"""

import sys
import os

def main():
    print("Screenshot Tool Framework Test")
    print("=" * 40)
    
    # 检查项目结构
    required_dirs = [
        'src',
        'src/core', 
        'src/ui',
        'src/themes',
        'config',
        'data'
    ]
    
    required_files = [
        'main.py',
        'requirements.txt',
        'src/app.py',
        'src/core/auth_manager.py',
        'src/core/config_manager.py',
        'src/themes/theme_manager.py',
        'src/ui/login_window.py',
        'src/ui/register_window.py'
    ]
    
    print("Checking directory structure...")
    missing_dirs = 0
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"[OK] {dir_path}")
        else:
            print(f"[MISSING] {dir_path}")
            missing_dirs += 1
            
    print("\nChecking core files...")
    missing_files = 0
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"[OK] {file_path}")
        else:
            print(f"[MISSING] {file_path}")
            missing_files += 1
            
    # 测试Python基础功能
    print("\nTesting core functionality...")
    
    try:
        # 测试JSON操作
        import json
        test_data = {'theme': 'starry', 'test': True}
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed = json.loads(json_str)
        print(f"[OK] JSON operations: {parsed}")
        
        # 测试文件操作
        os.makedirs('test_temp', exist_ok=True)
        with open('test_temp/test.txt', 'w', encoding='utf-8') as f:
            f.write('test file content')
        
        with open('test_temp/test.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"[OK] File operations: {content}")
        
        # 清理测试文件
        os.remove('test_temp/test.txt')
        os.rmdir('test_temp')
        
        # 测试加密功能
        import hashlib
        test_password = "123456"
        hashed = hashlib.sha256(test_password.encode('utf-8')).hexdigest()
        print(f"[OK] Password hashing: {hashed[:10]}...")
        
        # 测试正则表达式
        import re
        phone_pattern = r'^1[3-9]\d{9}$'
        test_phone = "13800138000"
        is_valid = bool(re.match(phone_pattern, test_phone))
        print(f"[OK] Phone validation: {is_valid}")
        
        print("\n" + "="*40)
        print("Framework Test Results:")
        print(f"Missing directories: {missing_dirs}")
        print(f"Missing files: {missing_files}")
        
        if missing_dirs == 0 and missing_files == 0:
            print("Status: SUCCESS - Framework structure is complete!")
            print("\nNext steps:")
            print("1. Install PyQt6: pip install PyQt6")
            print("2. Run main app: python main.py")
            return True
        else:
            print("Status: WARNING - Some files/directories missing")
            return False
            
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    if not success:
        sys.exit(1)