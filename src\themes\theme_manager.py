#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器
"""

from PyQt6.QtCore import QObject, pyqtSignal


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号定义
    theme_changed = pyqtSignal(str)  # 主题变更信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = 'starry'  # 默认螺旋星空主题
        
        # 定义主题配置
        self.themes = {
            'starry': {
                'name': '螺旋星空',
                'primary_gradient': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e3c72, stop:1 #2a5298)',
                'secondary_gradient': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #667eea, stop:1 #764ba2)',
                'text_color': '#ffffff',
                'secondary_text': 'rgba(255, 255, 255, 0.8)',
                'border_color': 'rgba(255, 255, 255, 0.3)',
                'hover_color': 'rgba(255, 255, 255, 0.1)',
                'glass_bg': 'rgba(255, 255, 255, 0.1)',
                'glass_border': 'rgba(255, 255, 255, 0.2)'
            },
            'kitty': {
                'name': '粉色浪漫Hello Kitty',
                'primary_gradient': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff9a9e, stop:0.5 #fecfef, stop:1 #fecfef)',
                'secondary_gradient': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff9a9e, stop:1 #fecfef)',
                'text_color': '#333333',
                'secondary_text': 'rgba(51, 51, 51, 0.8)',
                'border_color': 'rgba(255, 192, 203, 0.5)',
                'hover_color': 'rgba(255, 255, 255, 0.3)',
                'glass_bg': 'rgba(255, 255, 255, 0.6)',
                'glass_border': 'rgba(255, 192, 203, 0.3)'
            }
        }
        
    def get_available_themes(self):
        """获取可用主题列表"""
        return [(key, theme['name']) for key, theme in self.themes.items()]
        
    def set_theme(self, theme_name):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
            
    def get_current_theme(self):
        """获取当前主题"""
        return self.current_theme
        
    def get_theme_config(self, theme_name=None):
        """获取主题配置"""
        if theme_name is None:
            theme_name = self.current_theme
        return self.themes.get(theme_name, self.themes['starry'])
        
    def get_window_style(self, theme_name=None):
        """获取窗口样式"""
        config = self.get_theme_config(theme_name)
        
        return f"""
            QWidget {{
                background: {config['primary_gradient']};
                color: {config['text_color']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }}
            
            QLabel {{
                color: {config['text_color']};
            }}
            
            QFrame {{
                background: {config['glass_bg']};
                border: 1px solid {config['glass_border']};
                border-radius: 10px;
            }}
            
            QLineEdit {{
                background: {config['glass_bg']};
                border: 2px solid {config['border_color']};
                border-radius: 8px;
                padding: 8px 12px;
                color: {config['text_color']};
                font-size: 13px;
            }}
            
            QLineEdit:focus {{
                border-color: {config['glass_border']};
                background: {config['hover_color']};
            }}
            
            QPushButton {{
                background: {config['secondary_gradient']};
                color: {config['text_color']};
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
            }}
            
            QPushButton:hover {{
                background: {config['hover_color']};
            }}
            
            QPushButton:pressed {{
                background: {config['border_color']};
            }}
            
            QPushButton:disabled {{
                background: {config['border_color']};
                color: {config['secondary_text']};
            }}
            
            QComboBox {{
                background: {config['glass_bg']};
                border: 2px solid {config['border_color']};
                border-radius: 8px;
                padding: 8px 12px;
                color: {config['text_color']};
            }}
            
            QComboBox::drop-down {{
                border: none;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border: none;
            }}
        """
        
    def get_tray_icon_style(self):
        """获取系统托盘图标样式（根据主题返回不同图标）"""
        if self.current_theme == 'starry':
            return "assets/icon_starry.png"
        else:
            return "assets/icon_kitty.png"