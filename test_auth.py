# -*- coding: utf-8 -*-
"""
测试用户认证功能（无GUI版本）
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_mock_qt_components():
    """创建模拟的Qt组件"""
    
    class MockQObject:
        def __init__(self):
            pass
    
    class MockSignal:
        def __init__(self, *args):
            self.handlers = []
        
        def connect(self, handler):
            self.handlers.append(handler)
        
        def emit(self, *args):
            for handler in self.handlers:
                try:
                    handler(*args)
                except:
                    pass
    
    # 模拟PyQt6模块
    import sys
    from types import ModuleType
    
    # 创建模拟模块
    mock_pyqt6 = ModuleType('PyQt6')
    mock_qtcore = ModuleType('PyQt6.QtCore')
    
    mock_qtcore.QObject = MockQObject
    mock_qtcore.pyqtSignal = MockSignal
    
    mock_pyqt6.QtCore = mock_qtcore
    
    sys.modules['PyQt6'] = mock_pyqt6
    sys.modules['PyQt6.QtCore'] = mock_qtcore

def test_auth_system():
    """测试认证系统"""
    print("Testing Authentication System")
    print("=" * 40)
    
    # 创建模拟Qt组件
    create_mock_qt_components()
    
    # 导入认证管理器
    from src.core.auth_manager import AuthManager
    
    # 创建认证管理器实例
    auth_manager = AuthManager()
    
    print("1. Testing user registration...")
    
    # 测试注册
    phone = "13800138000"
    password = "123456"
    
    success, message, _ = auth_manager.register(phone, password)
    print(f"   Registration result: {success}")
    print(f"   Message: {message}")
    
    if not success:
        print("   Registration failed, trying with different phone...")
        phone = f"138001380{len(str(hash(phone))) % 10:02d}"
        success, message, _ = auth_manager.register(phone, password)
        print(f"   Second attempt: {success}, {message}")
    
    print("\n2. Testing user login...")
    
    # 测试登录
    success, message, user_data = auth_manager.login(phone, password)
    print(f"   Login result: {success}")
    print(f"   Message: {message}")
    
    if success and user_data:
        print(f"   User ID: {user_data.get('id')}")
        print(f"   Phone: {user_data.get('phone')}")
        print(f"   Login status: {auth_manager.is_logged_in()}")
    
    print("\n3. Testing wrong password...")
    
    # 测试错误密码
    success, message, _ = auth_manager.login(phone, "wrong_password")
    print(f"   Wrong password result: {success}")
    print(f"   Message: {message}")
    
    print("\n4. Testing session management...")
    
    # 测试会话保存和加载
    if auth_manager.is_logged_in():
        print("   Saving session...")
        auth_manager._save_session()
        
        print("   Logging out...")
        auth_manager.logout()
        print(f"   After logout: {auth_manager.is_logged_in()}")
        
        print("   Loading saved session...")
        session_loaded = auth_manager.load_session()
        print(f"   Session loaded: {session_loaded}")
        print(f"   After loading: {auth_manager.is_logged_in()}")
    
    return True

def test_theme_system():
    """测试主题系统"""
    print("\n" + "=" * 40)
    print("Testing Theme System")
    print("=" * 40)
    
    # 创建模拟Qt组件
    create_mock_qt_components()
    
    from src.themes.theme_manager import ThemeManager
    
    theme_manager = ThemeManager()
    
    print("1. Testing available themes...")
    themes = theme_manager.get_available_themes()
    for theme_id, theme_name in themes:
        print(f"   {theme_id}: {theme_name}")
    
    print("\n2. Testing theme switching...")
    
    # 测试螺旋星空主题
    theme_manager.set_theme('starry')
    current = theme_manager.get_current_theme()
    print(f"   Set to starry theme: {current}")
    
    # 测试Hello Kitty主题
    theme_manager.set_theme('kitty')
    current = theme_manager.get_current_theme()
    print(f"   Set to kitty theme: {current}")
    
    print("\n3. Testing theme styles...")
    
    # 获取主题样式
    starry_style = theme_manager.get_window_style('starry')
    kitty_style = theme_manager.get_window_style('kitty')
    
    print(f"   Starry theme style length: {len(starry_style)} characters")
    print(f"   Kitty theme style length: {len(kitty_style)} characters")
    
    # 测试主题配置
    starry_config = theme_manager.get_theme_config('starry')
    kitty_config = theme_manager.get_theme_config('kitty')
    
    print(f"   Starry theme text color: {starry_config['text_color']}")
    print(f"   Kitty theme text color: {kitty_config['text_color']}")
    
    return True

def test_config_system():
    """测试配置系统"""
    print("\n" + "=" * 40)
    print("Testing Config System")
    print("=" * 40)
    
    # 创建模拟Qt组件
    create_mock_qt_components()
    
    from src.core.config_manager import ConfigManager
    
    config_manager = ConfigManager()
    
    print("1. Loading configuration...")
    config_manager.load_config()
    
    # 获取所有配置
    all_config = config_manager.get_all()
    print(f"   Total config items: {len(all_config)}")
    
    print("\n2. Testing config get/set...")
    
    # 测试配置读写
    original_theme = config_manager.get('theme')
    print(f"   Original theme: {original_theme}")
    
    config_manager.set('theme', 'kitty')
    new_theme = config_manager.get('theme')
    print(f"   After setting: {new_theme}")
    
    # 测试默认值
    test_value = config_manager.get('non_existent_key', 'default_value')
    print(f"   Default value test: {test_value}")
    
    print("\n3. Key configuration items:")
    key_items = ['theme', 'hotkey', 'save_path', 'auto_save']
    for key in key_items:
        value = config_manager.get(key)
        print(f"   {key}: {value}")
    
    return True

def main():
    """主测试函数"""
    print("Screenshot Tool Core Systems Test")
    print("Running without PyQt6 GUI components")
    print("=" * 50)
    
    try:
        # 测试认证系统
        test_auth_system()
        
        # 测试主题系统
        test_theme_system()
        
        # 测试配置系统
        test_config_system()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("\nCore systems are working properly:")
        print("✓ User authentication (register/login)")
        print("✓ Session management")
        print("✓ Theme switching")
        print("✓ Configuration management")
        print("\nReady for GUI implementation!")
        
        return True
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    if not success:
        sys.exit(1)